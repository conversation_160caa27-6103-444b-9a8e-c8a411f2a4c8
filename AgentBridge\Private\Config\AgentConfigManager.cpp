// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/AgentConfigManager.h"
#include "Config/AgentBridgeConfigSettings.h"
#include "Models/AgentModels.h" // For FAgentCreateRequest and other potential structs
#include "HttpModule.h"
#include "Interfaces/IHttpResponse.h"
#include "JsonUtilities.h" // For FJsonObjectConverter
#include "HAL/PlatformFileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "GenericPlatform/GenericPlatformHttp.h" // For FGenericPlatformHttp::UrlEncode

DEFINE_LOG_CATEGORY_STATIC(LogAgentConfigManager, Log, All);

void UAgentConfigManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    UE_LOG(LogAgentConfigManager, Log, TEXT("AgentConfigManager Initialized."));
    // Configurations can be loaded explicitly by calling LoadAllConfigurationsRecursive()
    // Or, trigger a default load here if desired:
    // LoadAllConfigurationsRecursive();
}

void UAgentConfigManager::Deinitialize()
{
    UE_LOG(LogAgentConfigManager, Log, TEXT("AgentConfigManager Deinitialized."));
    // Cancel any pending HTTP requests if necessary
    Super::Deinitialize();
}

const UAgentBridgeConfigSettings* UAgentConfigManager::GetSettings() const
{
    return GetDefault<UAgentBridgeConfigSettings>();
}

void UAgentConfigManager::LoadAllConfigurationsRecursive(const FString& OptionalPathInRepo, int32 MaxRecursionDepth)
{
    RawConfigData.Empty();

    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings)
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Failed to get AgentBridgeConfigSettings."));
        OnAllConfigsLoaded.Broadcast(false);
        return;
    }

    FString RootPathToLoad = OptionalPathInRepo;
    if (RootPathToLoad.IsEmpty())
    {
        RootPathToLoad = Settings->DefaultRootConfigPathInRepo;
    }
    // Ensure path ends with a slash if not empty, for consistency
    if (!RootPathToLoad.IsEmpty() && !RootPathToLoad.EndsWith(TEXT("/")))
    {
        RootPathToLoad += TEXT("/");
    }

    if (Settings->bPreferLocalConfigs || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogAgentConfigManager, Log, TEXT("Loading configurations from local directory, starting at relative path: '%s' in '%s'"), *RootPathToLoad, *Settings->LocalConfigDirectoryPath);
        LoadConfigurationsFromLocalRecursive(Settings->LocalConfigDirectoryPath, RootPathToLoad, 0, MaxRecursionDepth);
        // Local loading is synchronous for this example
        OnAllConfigsLoaded.Broadcast(RawConfigData.Num() > 0);
    }
    else
    {
        UE_LOG(LogAgentConfigManager, Log, TEXT("Loading configurations from backend: %s, starting at repo path: '%s'"), *Settings->BackendConfigServiceURL, *RootPathToLoad);
        TSharedRef<int32> InFlightRequests = MakeShared<int32>(0);
        TSharedRef<bool> OverallSuccessFlag = MakeShared<bool>(true); // Assume success until a failure
        FetchFileListRecursive(RootPathToLoad, 0, MaxRecursionDepth, InFlightRequests, OverallSuccessFlag);
    }
}

void UAgentConfigManager::FetchFileListRecursive(const FString& PathInRepo, int32 CurrentDepth, int32 MaxDepth, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag)
{
    if (CurrentDepth > MaxDepth)
    {
        return; // Max depth reached
    }

    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("BackendConfigServiceURL is not set."));
        *OverallSuccessFlag = false;
        if (*InFlightRequests == 0) OnAllConfigsLoaded.Broadcast(*OverallSuccessFlag);
        return;
    }

    FString EncodedPath = FGenericPlatformHttp::UrlEncode(PathInRepo);
    FString ListUrl = FString::Printf(TEXT("%s/api/v1/configs/list?path=%s"), *Settings->BackendConfigServiceURL, *EncodedPath);

    FHttpModule& HttpModule = FHttpModule::Get();
    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = HttpModule.CreateRequest();
    HttpRequest->SetURL(ListUrl);
    HttpRequest->SetVerb(TEXT("GET"));
    if (!Settings->BackendAPIKey.IsEmpty())
    {
        HttpRequest->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
    }

    HttpRequest->OnProcessRequestComplete().BindUObject(this, &UAgentConfigManager::OnFileListRequestComplete, PathInRepo, CurrentDepth, MaxDepth, InFlightRequests, OverallSuccessFlag);

    (*InFlightRequests)++;
    UE_LOG(LogAgentConfigManager, Log, TEXT("Requesting file list for path: '%s' from URL: %s"), *PathInRepo, *ListUrl);
    if (!HttpRequest->ProcessRequest())
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Failed to process list request for path: %s"), *PathInRepo);
        (*InFlightRequests)--;
        *OverallSuccessFlag = false;
        if (*InFlightRequests == 0) OnAllConfigsLoaded.Broadcast(*OverallSuccessFlag);
    }
}

void UAgentConfigManager::OnFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FString PathToList, int32 CurrentDepth, int32 MaxDepth, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag)
{
    (*InFlightRequests)--;

    if (!bWasSuccessful || !Response.IsValid() || Response->GetResponseCode() != 200)
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("File list request for '%s' failed. URL: %s, Code: %d"), *PathToList, *Request->GetURL(), Response.IsValid() ? Response->GetResponseCode() : -1);
        *OverallSuccessFlag = false;
        if (*InFlightRequests == 0) OnAllConfigsLoaded.Broadcast(*OverallSuccessFlag);
        return;
    }

    FString JsonString = Response->GetContentAsString();
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Failed to parse file list JSON for path '%s': %s"), *PathToList, *JsonString.Left(200));
        *OverallSuccessFlag = false;
        if (*InFlightRequests == 0) OnAllConfigsLoaded.Broadcast(*OverallSuccessFlag);
        return;
    }

    // Fetch individual files
    const TArray<TSharedPtr<FJsonValue>>* FilesJsonArray;
    if (JsonObject->TryGetArrayField(TEXT("files"), FilesJsonArray))
    {
        for (const TSharedPtr<FJsonValue>& FileValue : *FilesJsonArray)
        {
            FString FileName = FileValue->AsString();
            if (FileName.EndsWith(TEXT(".json")))
            {
                FString FilePathInRepo = PathToList + FileName;

                // Request file content
                const UAgentBridgeConfigSettings* Settings = GetSettings();
                FString EncodedFilePath = FGenericPlatformHttp::UrlEncode(FilePathInRepo);
                FString FileUrl = FString::Printf(TEXT("%s/api/v1/configs/file?path=%s"), *Settings->BackendConfigServiceURL, *EncodedFilePath);

                FHttpModule& HttpModule = FHttpModule::Get();
                TSharedRef<IHttpRequest, ESPMode::ThreadSafe> FileReq = HttpModule.CreateRequest();
                FileReq->SetURL(FileUrl);
                FileReq->SetVerb(TEXT("GET"));
                if (!Settings->BackendAPIKey.IsEmpty())
                {
                    FileReq->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
                }
                FileReq->OnProcessRequestComplete().BindUObject(this, &UAgentConfigManager::OnFileContentRequestComplete, FilePathInRepo, InFlightRequests, OverallSuccessFlag);

                (*InFlightRequests)++;
                UE_LOG(LogAgentConfigManager, Log, TEXT("Requesting file content for: '%s' from URL: %s"), *FilePathInRepo, *FileUrl);
                if (!FileReq->ProcessRequest())
                {
                    UE_LOG(LogAgentConfigManager, Error, TEXT("Failed to process content request for file: %s"), *FilePathInRepo);
                    (*InFlightRequests)--;
                    *OverallSuccessFlag = false;
                }
            }
        }
    }

    // Recursively fetch for subfolders
    const TArray<TSharedPtr<FJsonValue>>* FoldersJsonArray;
    if (JsonObject->TryGetArrayField(TEXT("folders"), FoldersJsonArray))
    {
        for (const TSharedPtr<FJsonValue>& FolderValue : *FoldersJsonArray)
        {
            FString FolderName = FolderValue->AsString();
            if (!FolderName.IsEmpty())
            {
                FString SubFolderPath = PathToList + FolderName + TEXT("/");
                FetchFileListRecursive(SubFolderPath, CurrentDepth + 1, MaxDepth, InFlightRequests, OverallSuccessFlag);
            }
        }
    }

    if (*InFlightRequests == 0) OnAllConfigsLoaded.Broadcast(*OverallSuccessFlag);
}

void UAgentConfigManager::OnFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FString FilePathInRepo, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag)
{
    (*InFlightRequests)--;

    if (bWasSuccessful && Response.IsValid() && Response->GetResponseCode() == 200)
    {
        FString JsonContent = Response->GetContentAsString();
        RawConfigData.Add(FilePathInRepo, JsonContent);
        UE_LOG(LogAgentConfigManager, Log, TEXT("Successfully fetched config: %s"), *FilePathInRepo);
    }
    else
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Failed to fetch config content for '%s'. URL: %s, Code: %d"), *FilePathInRepo, *Request->GetURL(), Response.IsValid() ? Response->GetResponseCode() : -1);
        *OverallSuccessFlag = false;
    }

    if (*InFlightRequests == 0)
    {
        UE_LOG(LogAgentConfigManager, Log, TEXT("All configuration requests finished. Overall success: %s. Loaded %d configs."), (*OverallSuccessFlag ? TEXT("true") : TEXT("false")), RawConfigData.Num());
        OnAllConfigsLoaded.Broadcast(*OverallSuccessFlag);
    }
}

void UAgentConfigManager::ListContentsAtPath(const FString& PathInRepo, FFileListResponseDelegate CompletionDelegate)
{
    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("BackendConfigServiceURL is not set for ListContentsAtPath."));
        CompletionDelegate.ExecuteIfBound(false, PathInRepo, {}, {});
        return;
    }

    FString CleanPathInRepo = PathInRepo;
    if (!CleanPathInRepo.IsEmpty() && !CleanPathInRepo.EndsWith(TEXT("/")))
    {
        CleanPathInRepo += TEXT("/");
    }
    FString EncodedPath = FGenericPlatformHttp::UrlEncode(CleanPathInRepo);
    FString ListUrl = FString::Printf(TEXT("%s/api/v1/configs/list?path=%s"), *Settings->BackendConfigServiceURL, *EncodedPath);

    FHttpModule& HttpModule = FHttpModule::Get();
    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = HttpModule.CreateRequest();
    HttpRequest->SetURL(ListUrl);
    HttpRequest->SetVerb(TEXT("GET"));
    if (!Settings->BackendAPIKey.IsEmpty())
    {
        HttpRequest->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
    }
    HttpRequest->OnProcessRequestComplete().BindUObject(this, &UAgentConfigManager::OnPublicFileListRequestComplete, CompletionDelegate, CleanPathInRepo);

    UE_LOG(LogAgentConfigManager, Log, TEXT("Public API: Requesting file list for path: '%s' from URL: %s"), *CleanPathInRepo, *ListUrl);
    if (!HttpRequest->ProcessRequest())
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Public API: Failed to process list request for path: %s"), *CleanPathInRepo);
        CompletionDelegate.ExecuteIfBound(false, CleanPathInRepo, {}, {});
    }
}

void UAgentConfigManager::OnPublicFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FFileListResponseDelegate UserDelegate, FString OriginalPath)
{
    if (!bWasSuccessful || !Response.IsValid() || Response->GetResponseCode() != 200)
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Public API: File list request for '%s' failed. URL: %s, Code: %d"), *OriginalPath, *Request->GetURL(), Response.IsValid() ? Response->GetResponseCode() : -1);
        UserDelegate.ExecuteIfBound(false, OriginalPath, {}, {});
        return;
    }

    FString JsonString = Response->GetContentAsString();
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    TArray<FString> FileNames, FolderNames;
    if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
    {
        const TArray<TSharedPtr<FJsonValue>>* FilesJsonArray;
        if (JsonObject->TryGetArrayField(TEXT("files"), FilesJsonArray))
        {
            for (const auto& Val : *FilesJsonArray) FileNames.Add(Val->AsString());
        }
        const TArray<TSharedPtr<FJsonValue>>* FoldersJsonArray;
        if (JsonObject->TryGetArrayField(TEXT("folders"), FoldersJsonArray))
        {
            for (const auto& Val : *FoldersJsonArray) FolderNames.Add(Val->AsString());
        }
        UserDelegate.ExecuteIfBound(true, OriginalPath, FileNames, FolderNames);
    }
    else
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Public API: Failed to parse file list JSON for path '%s': %s"), *OriginalPath, *JsonString.Left(200));
        UserDelegate.ExecuteIfBound(false, OriginalPath, {}, {});
    }
}

void UAgentConfigManager::FetchAndCacheConfig(const FString& FilePathInRepo, FFileContentResponseDelegate CompletionDelegate)
{
    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("BackendConfigServiceURL is not set for FetchAndCacheConfig."));
        CompletionDelegate.ExecuteIfBound(false, FilePathInRepo, TEXT(""));
        return;
    }

    FString EncodedFilePath = FGenericPlatformHttp::UrlEncode(FilePathInRepo);
    FString FileUrl = FString::Printf(TEXT("%s/api/v1/configs/file?path=%s"), *Settings->BackendConfigServiceURL, *EncodedFilePath);

    FHttpModule& HttpModule = FHttpModule::Get();
    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = HttpModule.CreateRequest();
    HttpRequest->SetURL(FileUrl);
    HttpRequest->SetVerb(TEXT("GET"));
    if (!Settings->BackendAPIKey.IsEmpty())
    {
        HttpRequest->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
    }
    HttpRequest->OnProcessRequestComplete().BindUObject(this, &UAgentConfigManager::OnPublicFileContentRequestComplete, CompletionDelegate, FilePathInRepo);

    UE_LOG(LogAgentConfigManager, Log, TEXT("Public API: Requesting file content for: '%s' from URL: %s"), *FilePathInRepo, *FileUrl);
    if (!HttpRequest->ProcessRequest())
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Public API: Failed to process content request for file: %s"), *FilePathInRepo);
        CompletionDelegate.ExecuteIfBound(false, FilePathInRepo, TEXT(""));
    }
}

void UAgentConfigManager::OnPublicFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FFileContentResponseDelegate UserDelegate, FString OriginalFilePath)
{
    if (bWasSuccessful && Response.IsValid() && Response->GetResponseCode() == 200)
    {
        FString JsonContent = Response->GetContentAsString();
        RawConfigData.Add(OriginalFilePath, JsonContent); // Cache it
        UE_LOG(LogAgentConfigManager, Log, TEXT("Public API: Successfully fetched and cached config: %s"), *OriginalFilePath);
        UserDelegate.ExecuteIfBound(true, OriginalFilePath, JsonContent);
    }
    else
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Public API: Failed to fetch config content for '%s'. URL: %s, Code: %d"), *OriginalFilePath, *Request->GetURL(), Response.IsValid() ? Response->GetResponseCode() : -1);
        UserDelegate.ExecuteIfBound(false, OriginalFilePath, TEXT(""));
    }
}


void UAgentConfigManager::LoadConfigurationsFromLocalRecursive(const FString& RootDirectory, const FString& CurrentRelativePath, int32 CurrentDepth, int32 MaxDepth)
{
    if (CurrentDepth > MaxDepth) return;

    IFileManager& FileManager = IFileManager::Get(); // Corrected this line
    FString CurrentFullDirectory = FPaths::Combine(RootDirectory, CurrentRelativePath);

    // Find files
    TArray<FString> FoundFiles;
    FString FileSearchPattern = CurrentFullDirectory / TEXT("*.json");
    FileManager.FindFiles(FoundFiles, *FileSearchPattern, true, false);
    for (const FString& FullFilePath : FoundFiles)
    {
        FString FileName = FPaths::GetCleanFilename(FullFilePath);
        FString PathInRepoStyle = CurrentRelativePath + FileName; // Store with relative path as key
        ReadAndCacheLocalFile(FullFilePath, PathInRepoStyle);
    }

    // Find directories for recursion
    if (CurrentDepth < MaxDepth)
    {
        TArray<FString> FoundDirectories;
        FString DirSearchPattern = CurrentFullDirectory / TEXT("*");
        FileManager.FindFiles(FoundDirectories, *DirSearchPattern, false, true); // Only directories
        for (const FString& DirName : FoundDirectories) // These are just names, not full paths
        {
            FString NextRelativePath = CurrentRelativePath + DirName + TEXT("/");
            LoadConfigurationsFromLocalRecursive(RootDirectory, NextRelativePath, CurrentDepth + 1, MaxDepth);
        }
    }
}

bool UAgentConfigManager::ReadAndCacheLocalFile(const FString& FullFilePath, const FString& PathInRepo)
{
    FString JsonString;
    if (!FFileHelper::LoadFileToString(JsonString, *FullFilePath))
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Failed to read local config file: %s"), *FullFilePath);
        return false;
    }
    RawConfigData.Add(PathInRepo, JsonString);
    UE_LOG(LogAgentConfigManager, Log, TEXT("Successfully read and cached local config: %s (from %s)"), *PathInRepo, *FullFilePath);
    return true;
}

template<typename TStructType>
bool UAgentConfigManager::ParseConfigData(const FString& JsonString, const FString& ConfigName, TStructType& OutStruct) const
{
    if (!FJsonObjectConverter::JsonObjectStringToUStruct(JsonString, &OutStruct, 0, 0))
    {
        UE_LOG(LogAgentConfigManager, Error, TEXT("Failed to parse JSON for config '%s'. JSON: %s"), *ConfigName, *JsonString.Left(500));
        return false;
    }
    return true;
}

bool UAgentConfigManager::GetAgentCreateConfiguration(const FString& ConfigFilePathInRepo, FAgentCreateRequest& OutConfig) const
{
    return GetConfiguration<FAgentCreateRequest>(ConfigFilePathInRepo, OutConfig);
}

const TMap<FString, FString>& UAgentConfigManager::GetRawConfigCache() const
{
    return RawConfigData;
}

TMap<FString, FAgentCreateRequest> UAgentConfigManager::GetParsedAgentConfigCache() const
{
    TMap<FString, FAgentCreateRequest> ParsedConfigs;

    // Iterate through all raw JSON configurations
    for (const TPair<FString, FString>& ConfigPair : RawConfigData)
    {
        const FString& ConfigPath = ConfigPair.Key;
        const FString& JsonContent = ConfigPair.Value;

        // Try to parse the JSON as an FAgentCreateRequest
        FAgentCreateRequest AgentConfig;
        if (ParseConfigData(JsonContent, ConfigPath, AgentConfig))
        {
            // Add to the parsed map only if parsing was successful
            ParsedConfigs.Add(ConfigPath, AgentConfig);
        }
        else
        {
            // Log that this config couldn't be parsed as an FAgentCreateRequest
            UE_LOG(LogAgentConfigManager, Verbose, TEXT("Config '%s' could not be parsed as an FAgentCreateRequest."), *ConfigPath);
        }
    }

    return ParsedConfigs;
}
