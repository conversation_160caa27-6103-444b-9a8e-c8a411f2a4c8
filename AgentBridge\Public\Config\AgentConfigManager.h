// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/EngineSubsystem.h"
#include "Interfaces/IHttpRequest.h" // For FHttpRequestPtr, FHttpResponsePtr
#include "AgentConfigManager.generated.h"

// Forward declarations
class UAgentBridgeConfigSettings;
struct FAgentCreateRequest; // Example struct

// Delegates for asynchronous operations
DECLARE_DYNAMIC_DELEGATE_FourParams(FFileListResponseDelegate, bool, bSuccess, const FString&, Path, const TArray<FString>&, FileNames, const TArray<FString>&, FolderNames);
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FFileContentResponseDelegate, bool, bSuccess, const FString&, FilePath, const FString&, JsonContent);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAllConfigsRequestCompleted, bool, bOverallSuccess); // For recursive loading

UCLASS()
class AGENTBRIDGE_API UAgentConfigManager : public UEngineSubsystem
{
    GENERATED_BODY()

public:
    //~ Begin USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    //~ End USubsystem interface

    /**
     * Attempts to load all configurations starting from the DefaultRootConfigPathInRepo (from settings) or a specified path.
     * This will recursively fetch configurations up to a certain depth.
     * @param OptionalPathInRepo Override the default root path from settings.
     * @param MaxRecursionDepth How many levels of subfolders to explore. 0 means only the given path.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    void LoadAllConfigurationsRecursive(const FString& OptionalPathInRepo = TEXT(""), int32 MaxRecursionDepth = 1);

    /**
     * Lists files and folders at a specific path within the configured GitHub repository (via backend).
     * @param PathInRepo The path within the repository to list.
     * @param CompletionDelegate Delegate to call when the list operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    void ListContentsAtPath(const FString& PathInRepo, FFileListResponseDelegate CompletionDelegate);

    /**
     * Fetches and caches a single configuration file from a specific path within the GitHub repository (via backend).
     * @param FilePathInRepo The full path to the configuration file within the repository.
     * @param CompletionDelegate Delegate to call when the fetch operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration")
    void FetchAndCacheConfig(const FString& FilePathInRepo, FFileContentResponseDelegate CompletionDelegate);

    /**
     * Gets a parsed configuration struct by its full path name (as used in the repository).
     * Assumes the configuration has been loaded and cached.
     * @param ConfigFilePathInRepo The full path of the configuration file (e.g., "MyAgents/GroupA/agent1.json").
     * @param OutStruct The structure to populate with configuration data.
     * @return True if the configuration was found and successfully parsed, false otherwise.
     */
    template<typename TStructType>
    bool GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const;

    // Example of a specific getter
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration", meta=(DisplayName="Get Agent Create Config By Path"))
    bool GetAgentCreateConfiguration(const FString& ConfigFilePathInRepo, FAgentCreateRequest& OutConfig) const;

    /**
     * Gets the raw configuration data cache map.
     * This provides direct access to all cached configuration JSON strings.
     * @return A const reference to the internal map of configuration data.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration", meta=(DisplayName="Get Raw Config Cache"))
    const TMap<FString, FString>& GetRawConfigCache() const;

    /**
     * Gets a map of parsed FAgentCreateRequest structures.
     * This provides direct access to all cached agent configurations as parsed objects.
     * Only configurations that can be successfully parsed as FAgentCreateRequest will be included.
     * @return A map of file paths to parsed FAgentCreateRequest structures.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|Configuration", meta=(DisplayName="Get Parsed Agent Config Cache"))
    TMap<FString, FAgentCreateRequest> GetParsedAgentConfigCache() const;

    /** Delegate broadcast when LoadAllConfigurationsRecursive completes. */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|Configuration")
    FOnAllConfigsRequestCompleted OnAllConfigsLoaded; // Renamed from OnConfigurationsLoaded for clarity

private:
    // Backend interaction
    void FetchFileListRecursive(const FString& PathInRepo, int32 CurrentDepth, int32 MaxDepth, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag);
    void OnFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FString PathToList, int32 CurrentDepth, int32 MaxDepth, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag);
    void OnFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FString FilePathInRepo, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag);

    // For single file/list requests via public API
    void OnPublicFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FFileListResponseDelegate UserDelegate, FString OriginalPath);
    void OnPublicFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FFileContentResponseDelegate UserDelegate, FString OriginalFilePath);

    // Local file operations
    void LoadConfigurationsFromLocalRecursive(const FString& RootDirectory, const FString& CurrentRelativePath, int32 CurrentDepth, int32 MaxDepth);
    bool ReadAndCacheLocalFile(const FString& FullFilePath, const FString& PathInRepo);

    // Common parsing logic
    template<typename TStructType>
    bool ParseConfigData(const FString& JsonString, const FString& ConfigName, TStructType& OutStruct) const;

    // Stores raw JSON string data for each config, keyed by full path in repo
    TMap<FString, FString> RawConfigData;

    const UAgentBridgeConfigSettings* GetSettings() const;
};

// Template implementation for GetConfiguration
template<typename TStructType>
bool UAgentConfigManager::GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const
{
    const FString* JsonString = RawConfigData.Find(ConfigFilePathInRepo);
    if (JsonString)
    {
        return ParseConfigData(*JsonString, ConfigFilePathInRepo, OutStruct);
    }
    // LogAgentConfigManager is defined in CPP, use UE_LOG directly or ensure LogAgentBridge is available if preferred.
    UE_LOG(LogTemp, Warning, TEXT("Configuration '%s' not found in cache."), *ConfigFilePathInRepo);
    return false;
}
