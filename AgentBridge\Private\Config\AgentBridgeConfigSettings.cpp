// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/AgentBridgeConfigSettings.h"

UAgentBridgeConfigSettings::UAgentBridgeConfigSettings()
{
    // Default values
    BackendConfigServiceURL = TEXT("");
    BackendAPIKey = TEXT("");
    DefaultRootConfigPathInRepo = TEXT(""); // Or a sensible default like "Configs/"
    bPreferLocalConfigs = false;
    LocalConfigDirectoryPath = FPaths::ProjectPluginsDir() / TEXT("AgentBridge/Content/ExampleConfigs/"); // Default example path
}

#if WITH_EDITOR
FText UAgentBridgeConfigSettings::GetSectionText() const
{
    return NSLOCTEXT("AgentBridgePlugin", "AgentBridgeConfigSettingsSection", "AgentBridge");
}

FText UAgentBridgeConfigSettings::GetSectionDescription() const
{
    return NSLOCTEXT("AgentBridgePlugin", "AgentBridgeConfigSettingsDescription", "Configure settings for the AgentBridge plugin, including the configuration service.");
}
#endif
