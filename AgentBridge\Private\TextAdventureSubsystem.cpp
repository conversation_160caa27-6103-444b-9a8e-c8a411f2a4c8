// Copyright Epic Games, Inc. All Rights Reserved.

#include "TextAdventureSubsystem.h"
#include "WebServiceSubsystem.h"
#include "AgentBridge.h"
#include "Engine/GameInstance.h"
#include "Engine/Engine.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

void UTextAdventureSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);

    // Get the web service subsystem
    WebServiceSubsystem = GetGameInstance()->GetSubsystem<UWebServiceSubsystem>();
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Failed to get WebServiceSubsystem"));
    }

    // Initialize state
    CurrentNarratorState = FNarratorState();
    LastResponse = FTextAdventureResponse();
    CurrentWorldId = TEXT("");

    UE_LOG(LogAgentBridge, Log, TEXT("TextAdventureSubsystem initialized"));
}

void UTextAdventureSubsystem::Deinitialize()
{
    WebServiceSubsystem = nullptr;
    Super::Deinitialize();
    UE_LOG(LogAgentBridge, Log, TEXT("TextAdventureSubsystem deinitialized"));
}

void UTextAdventureSubsystem::SetupTextAdventure(const FTextAdventureSetupRequest& SetupData)
{
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("WebServiceSubsystem is null"));
        OnTextAdventureSetup.Broadcast(false, FTextAdventureSetupResponse());
        return;
    }

    UE_LOG(LogAgentBridge, Log, TEXT("Setting up text adventure: %s"), *SetupData.NarratorId);

    // Create JSON payload
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("narrator_id"), SetupData.NarratorId);
    JsonObject->SetStringField(TEXT("world_id"), SetupData.WorldId);

    // Add chapter data
    TSharedPtr<FJsonObject> ChapterObject = MakeShareable(new FJsonObject);
    ChapterObject->SetStringField(TEXT("chapter_id"), SetupData.Chapter.ChapterId);
    ChapterObject->SetStringField(TEXT("title"), SetupData.Chapter.Title);
    ChapterObject->SetStringField(TEXT("system_prompt"), SetupData.Chapter.SystemPrompt);
    ChapterObject->SetStringField(TEXT("initial_scene"), SetupData.Chapter.InitialScene);

    // Add objectives array
    TArray<TSharedPtr<FJsonValue>> ObjectivesArray;
    for (const FString& Objective : SetupData.Chapter.Objectives)
    {
        ObjectivesArray.Add(MakeShareable(new FJsonValueString(Objective)));
    }
    ChapterObject->SetArrayField(TEXT("objectives"), ObjectivesArray);

    JsonObject->SetObjectField(TEXT("chapter"), ChapterObject);

    // Add world rules array
    TArray<TSharedPtr<FJsonValue>> RulesArray;
    for (const FString& Rule : SetupData.WorldRules)
    {
        RulesArray.Add(MakeShareable(new FJsonValueString(Rule)));
    }
    JsonObject->SetArrayField(TEXT("world_rules"), RulesArray);

    // Add agent IDs array
    TArray<TSharedPtr<FJsonValue>> AgentIdsArray;
    for (const FString& AgentId : SetupData.AgentIds)
    {
        AgentIdsArray.Add(MakeShareable(new FJsonValueString(AgentId)));
    }
    JsonObject->SetArrayField(TEXT("agent_ids"), AgentIdsArray);

    // Convert to JSON string
    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    // Store current setup data for response handling
    CurrentWorldId = SetupData.WorldId;

    // Make the request
    FString Endpoint = TEXT("/api/narrator/setup-text-adventure");
    WebServiceSubsystem->MakePostRequest(
        Endpoint,
        JsonString,
        FWebServiceResponse::CreateUObject(this, &UTextAdventureSubsystem::HandleSetupResponse)
    );
}

void UTextAdventureSubsystem::CreateNarrator(const FNarratorCreateRequest& NarratorData)
{
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("WebServiceSubsystem is null"));
        OnNarratorCreated.Broadcast(false, FNarratorState());
        return;
    }

    UE_LOG(LogAgentBridge, Log, TEXT("Creating narrator: %s"), *NarratorData.NarratorId);

    // Create JSON payload
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("narrator_id"), NarratorData.NarratorId);
    JsonObject->SetBoolField(TEXT("verbose"), NarratorData.bVerbose);

    // Convert to JSON string
    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    // Make the request
    FString Endpoint = TEXT("/api/narrator");
    WebServiceSubsystem->MakePostRequest(
        Endpoint,
        JsonString,
        FWebServiceResponse::CreateUObject(this, &UTextAdventureSubsystem::HandleCreateNarratorResponse)
    );
}

void UTextAdventureSubsystem::AddChapter(const FChapterConfig& ChapterData)
{
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("WebServiceSubsystem is null"));
        return;
    }

    if (CurrentNarratorState.NarratorId.IsEmpty())
    {
        UE_LOG(LogAgentBridge, Error, TEXT("No narrator is currently active"));
        return;
    }

    UE_LOG(LogAgentBridge, Log, TEXT("Adding chapter: %s"), *ChapterData.ChapterId);

    // Create JSON payload
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("chapter_id"), ChapterData.ChapterId);
    JsonObject->SetStringField(TEXT("title"), ChapterData.Title);
    JsonObject->SetStringField(TEXT("system_prompt"), ChapterData.SystemPrompt);
    JsonObject->SetStringField(TEXT("initial_scene"), ChapterData.InitialScene);

    // Add objectives array
    TArray<TSharedPtr<FJsonValue>> ObjectivesArray;
    for (const FString& Objective : ChapterData.Objectives)
    {
        ObjectivesArray.Add(MakeShareable(new FJsonValueString(Objective)));
    }
    JsonObject->SetArrayField(TEXT("objectives"), ObjectivesArray);

    // Convert to JSON string
    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    // Make the request
    FString Endpoint = FString::Printf(TEXT("/api/narrator/%s/chapters"), *CurrentNarratorState.NarratorId);
    WebServiceSubsystem->MakePostRequest(
        Endpoint,
        JsonString,
        FWebServiceResponse::CreateUObject(this, &UTextAdventureSubsystem::HandleAddChapterResponse)
    );
}

void UTextAdventureSubsystem::StartChapter(const FString& ChapterId)
{
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("WebServiceSubsystem is null"));
        OnChapterStarted.Broadcast(false, TEXT(""));
        return;
    }

    if (CurrentNarratorState.NarratorId.IsEmpty())
    {
        UE_LOG(LogAgentBridge, Error, TEXT("No narrator is currently active"));
        OnChapterStarted.Broadcast(false, TEXT(""));
        return;
    }

    UE_LOG(LogAgentBridge, Log, TEXT("Starting chapter: %s"), *ChapterId);

    // Create JSON payload
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("chapter_id"), ChapterId);

    // Convert to JSON string
    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    // Make the request
    FString Endpoint = FString::Printf(TEXT("/api/narrator/%s/start-chapter"), *CurrentNarratorState.NarratorId);
    WebServiceSubsystem->MakePostRequest(
        Endpoint,
        JsonString,
        FWebServiceResponse::CreateUObject(this, &UTextAdventureSubsystem::HandleStartChapterResponse)
    );
}

void UTextAdventureSubsystem::ProcessUserAction(const FString& WorldId, const FUserActionRequest& ActionData)
{
    if (!WebServiceSubsystem)
    {
        UE_LOG(LogAgentBridge, Error, TEXT("WebServiceSubsystem is null"));
        OnUserActionProcessed.Broadcast(false, FTextAdventureResponse());
        return;
    }

    if (CurrentNarratorState.NarratorId.IsEmpty())
    {
        UE_LOG(LogAgentBridge, Error, TEXT("No narrator is currently active"));
        OnUserActionProcessed.Broadcast(false, FTextAdventureResponse());
        return;
    }

    UE_LOG(LogAgentBridge, Log, TEXT("Processing user action: %s"), *ActionData.UserAction);

    // Create JSON payload
    TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
    JsonObject->SetStringField(TEXT("user_action"), ActionData.UserAction);
    JsonObject->SetStringField(TEXT("user_location"), ActionData.UserLocation);

    // Add agent IDs array
    TArray<TSharedPtr<FJsonValue>> AgentIdsArray;
    for (const FString& AgentId : ActionData.AgentIds)
    {
        AgentIdsArray.Add(MakeShareable(new FJsonValueString(AgentId)));
    }
    JsonObject->SetArrayField(TEXT("agent_ids"), AgentIdsArray);

    // Convert to JSON string
    FString JsonString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&JsonString);
    FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);

    // Make the request
    FString Endpoint = FString::Printf(TEXT("/api/narrator/%s/process-action?world_id=%s"),
        *CurrentNarratorState.NarratorId, *WorldId);
    WebServiceSubsystem->MakePostRequest(
        Endpoint,
        JsonString,
        FWebServiceResponse::CreateUObject(this, &UTextAdventureSubsystem::HandleUserActionResponse)
    );
}

void UTextAdventureSubsystem::HandleSetupResponse(bool bSuccess, int32 StatusCode, const FString& Response)
{
    FTextAdventureSetupResponse SetupResponse;

    if (bSuccess && StatusCode == 200)
    {
        UE_LOG(LogAgentBridge, Log, TEXT("Text adventure setup successful"));

        // Parse JSON response
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            SetupResponse.bSuccess = JsonObject->GetBoolField(TEXT("success"));
            SetupResponse.NarratorId = JsonObject->GetStringField(TEXT("narrator_id"));
            SetupResponse.WorldId = JsonObject->GetStringField(TEXT("world_id"));
            SetupResponse.InitialScene = JsonObject->GetStringField(TEXT("initial_scene"));

            if (JsonObject->HasField(TEXT("error")))
            {
                SetupResponse.Error = JsonObject->GetStringField(TEXT("error"));
            }

            // Update current state if successful
            if (SetupResponse.bSuccess)
            {
                CurrentNarratorState.NarratorId = SetupResponse.NarratorId;
                CurrentNarratorState.bHasActiveChapter = !SetupResponse.InitialScene.IsEmpty();
                CurrentWorldId = SetupResponse.WorldId;
            }
        }
        else
        {
            UE_LOG(LogAgentBridge, Error, TEXT("Failed to parse setup response JSON"));
            SetupResponse.bSuccess = false;
            SetupResponse.Error = TEXT("Failed to parse response");
        }
    }
    else
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Text adventure setup failed: %d - %s"), StatusCode, *Response);
        SetupResponse.bSuccess = false;
        SetupResponse.Error = FString::Printf(TEXT("HTTP %d: %s"), StatusCode, *Response);
    }

    OnTextAdventureSetup.Broadcast(SetupResponse.bSuccess, SetupResponse);
}

void UTextAdventureSubsystem::HandleCreateNarratorResponse(bool bSuccess, int32 StatusCode, const FString& Response)
{
    FNarratorState NarratorState;

    if (bSuccess && StatusCode == 200)
    {
        UE_LOG(LogAgentBridge, Log, TEXT("Narrator creation successful"));

        // Parse JSON response
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            NarratorState.NarratorId = JsonObject->GetStringField(TEXT("narrator_id"));
            NarratorState.bHasActiveChapter = false;
            NarratorState.NarrativeHistoryLength = 0;

            // Update current state
            CurrentNarratorState = NarratorState;
        }
        else
        {
            UE_LOG(LogAgentBridge, Error, TEXT("Failed to parse narrator response JSON"));
            bSuccess = false;
        }
    }
    else
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Narrator creation failed: %d - %s"), StatusCode, *Response);
        bSuccess = false;
    }

    OnNarratorCreated.Broadcast(bSuccess, NarratorState);
}

void UTextAdventureSubsystem::HandleAddChapterResponse(bool bSuccess, int32 StatusCode, const FString& Response)
{
    if (bSuccess && StatusCode == 200)
    {
        UE_LOG(LogAgentBridge, Log, TEXT("Chapter added successfully"));
    }
    else
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Chapter addition failed: %d - %s"), StatusCode, *Response);
    }
}

void UTextAdventureSubsystem::HandleStartChapterResponse(bool bSuccess, int32 StatusCode, const FString& Response)
{
    FString InitialScene;

    if (bSuccess && StatusCode == 200)
    {
        UE_LOG(LogAgentBridge, Log, TEXT("Chapter started successfully"));

        // Parse JSON response
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            bool bStartSuccess = JsonObject->GetBoolField(TEXT("success"));
            if (bStartSuccess)
            {
                InitialScene = JsonObject->GetStringField(TEXT("initial_scene"));
                CurrentNarratorState.bHasActiveChapter = true;

                // Update chapter info if available
                if (JsonObject->HasField(TEXT("chapter_info")))
                {
                    const TSharedPtr<FJsonObject>* ChapterInfoObj;
                    if (JsonObject->TryGetObjectField(TEXT("chapter_info"), ChapterInfoObj))
                    {
                        CurrentNarratorState.CurrentChapter.ChapterId = (*ChapterInfoObj)->GetStringField(TEXT("chapter_id"));
                        CurrentNarratorState.CurrentChapter.Title = (*ChapterInfoObj)->GetStringField(TEXT("title"));
                        CurrentNarratorState.CurrentChapter.Turn = (*ChapterInfoObj)->GetIntegerField(TEXT("turn"));
                    }
                }
            }
            else
            {
                bSuccess = false;
            }
        }
        else
        {
            UE_LOG(LogAgentBridge, Error, TEXT("Failed to parse chapter start response JSON"));
            bSuccess = false;
        }
    }
    else
    {
        UE_LOG(LogAgentBridge, Error, TEXT("Chapter start failed: %d - %s"), StatusCode, *Response);
        bSuccess = false;
    }

    OnChapterStarted.Broadcast(bSuccess, InitialScene);
}

void UTextAdventureSubsystem::HandleUserActionResponse(bool bSuccess, int32 StatusCode, const FString& Response)
{
    FTextAdventureResponse ActionResponse;

    if (bSuccess && StatusCode == 200)
    {
        UE_LOG(LogAgentBridge, Log, TEXT("User action processed successfully"));

        // Parse JSON response
        TSharedPtr<FJsonObject> JsonObject;
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response);

        if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
        {
            // Parse basic fields
            ActionResponse.Narration = JsonObject->GetStringField(TEXT("narration"));
            ActionResponse.ObservingAgents = JsonObject->GetIntegerField(TEXT("observing_agents"));
            ActionResponse.WorldTurn = JsonObject->GetIntegerField(TEXT("world_turn"));
            ActionResponse.Timestamp = JsonObject->GetStringField(TEXT("timestamp"));

            // Parse action evaluation
            const TSharedPtr<FJsonObject>* ActionEvalObj;
            if (JsonObject->TryGetObjectField(TEXT("action_evaluation"), ActionEvalObj))
            {
                ActionResponse.ActionEvaluation.bIsPossible = (*ActionEvalObj)->GetBoolField(TEXT("is_possible"));
                ActionResponse.ActionEvaluation.Reason = (*ActionEvalObj)->GetStringField(TEXT("reason"));
            }

            // Parse chapter info
            const TSharedPtr<FJsonObject>* ChapterInfoObj;
            if (JsonObject->TryGetObjectField(TEXT("chapter_info"), ChapterInfoObj))
            {
                ActionResponse.ChapterInfo.ChapterId = (*ChapterInfoObj)->GetStringField(TEXT("chapter_id"));
                ActionResponse.ChapterInfo.Title = (*ChapterInfoObj)->GetStringField(TEXT("title"));
                ActionResponse.ChapterInfo.Turn = (*ChapterInfoObj)->GetIntegerField(TEXT("turn"));

                // Update current narrator state
                CurrentNarratorState.CurrentChapter = ActionResponse.ChapterInfo;
            }

            // Parse agent reactions
            const TArray<TSharedPtr<FJsonValue>>* ReactionsArray;
            if (JsonObject->TryGetArrayField(TEXT("agent_reactions"), ReactionsArray))
            {
                for (const TSharedPtr<FJsonValue>& ReactionValue : *ReactionsArray)
                {
                    const TSharedPtr<FJsonObject>& ReactionObj = ReactionValue->AsObject();
                    if (ReactionObj.IsValid())
                    {
                        FAgentReaction Reaction;
                        Reaction.AgentId = ReactionObj->GetStringField(TEXT("agent_id"));
                        Reaction.AgentName = ReactionObj->GetStringField(TEXT("agent_name"));
                        Reaction.Observation = ReactionObj->GetStringField(TEXT("observation"));
                        Reaction.Action = ReactionObj->GetStringField(TEXT("action"));
                        Reaction.bIsDialogue = ReactionObj->GetBoolField(TEXT("is_dialogue"));
                        Reaction.DelaySeconds = ReactionObj->GetIntegerField(TEXT("delay_seconds"));

                        ActionResponse.AgentReactions.Add(Reaction);
                    }
                }
            }

            // Store the response
            LastResponse = ActionResponse;
        }
        else
        {
            UE_LOG(LogAgentBridge, Error, TEXT("Failed to parse user action response JSON"));
            bSuccess = false;
        }
    }
    else
    {
        UE_LOG(LogAgentBridge, Error, TEXT("User action processing failed: %d - %s"), StatusCode, *Response);
        bSuccess = false;
    }

    OnUserActionProcessed.Broadcast(bSuccess, ActionResponse);
}
